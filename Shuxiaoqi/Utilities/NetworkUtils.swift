//
//  NetworkUtils.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/7/29.
//

import Foundation
import Network
import SystemConfiguration

/// IP属地缓存管理器
class IPLocationCache {
    static let shared = IPLocationCache()
    private let userDefaults = UserDefaults.standard
    private let cacheKeyPrefix = "ip_location_cache_"
    private let cacheTimeKeyPrefix = "ip_location_time_"
    private let cacheValidDuration: TimeInterval = 24 * 60 * 60 // 24小时缓存有效期

    private init() {}

    /// 获取缓存的IP属地信息
    func getCachedLocation(for ip: String) -> String? {
        let cacheKey = cacheKeyPrefix + ip
        let timeKey = cacheTimeKeyPrefix + ip

        guard let cachedLocation = userDefaults.string(forKey: cacheKey),
              !cachedLocation.isEmpty else {
            return nil
        }

        let cacheTime = userDefaults.double(forKey: timeKey)
        let currentTime = Date().timeIntervalSince1970

        // 检查缓存是否过期
        if currentTime - cacheTime > cacheValidDuration {
            // 缓存过期，清除缓存
            userDefaults.removeObject(forKey: cacheKey)
            userDefaults.removeObject(forKey: timeKey)
            return nil
        }

        return cachedLocation
    }

    /// 缓存IP属地信息
    func cacheLocation(_ location: String, for ip: String) {
        let cacheKey = cacheKeyPrefix + ip
        let timeKey = cacheTimeKeyPrefix + ip
        let currentTime = Date().timeIntervalSince1970

        userDefaults.set(location, forKey: cacheKey)
        userDefaults.set(currentTime, forKey: timeKey)
        userDefaults.synchronize()
    }

    /// 清除所有IP属地缓存
    func clearAllCache() {
        let keys = userDefaults.dictionaryRepresentation().keys
        for key in keys {
            if key.hasPrefix(cacheKeyPrefix) || key.hasPrefix(cacheTimeKeyPrefix) {
                userDefaults.removeObject(forKey: key)
            }
        }
        userDefaults.synchronize()
    }

    /// 清除指定IP的缓存
    func clearCache(for ip: String) {
        let cacheKey = cacheKeyPrefix + ip
        let timeKey = cacheTimeKeyPrefix + ip
        userDefaults.removeObject(forKey: cacheKey)
        userDefaults.removeObject(forKey: timeKey)
        userDefaults.synchronize()
    }

    /// 检查指定IP是否有有效缓存
    func hasValidCache(for ip: String) -> Bool {
        return getCachedLocation(for: ip) != nil
    }

    /// 获取缓存统计信息
    func getCacheInfo() -> (count: Int, totalSize: Int) {
        let keys = userDefaults.dictionaryRepresentation().keys
        let cacheKeys = keys.filter { $0.hasPrefix(cacheKeyPrefix) }
        var totalSize = 0

        for key in cacheKeys {
            if let value = userDefaults.string(forKey: key) {
                totalSize += value.count
            }
        }

        return (count: cacheKeys.count, totalSize: totalSize)
    }
}

/// 网络相关工具类
struct NetworkUtils {
    
    /// 获取当前设备的本地IP地址
    static func getLocalIPAddress() -> String? {
        var address: String?
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        
        guard getifaddrs(&ifaddr) == 0 else {
            return nil
        }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let interface = ptr?.pointee else { continue }
            
            let addrFamily = interface.ifa_addr.pointee.sa_family
            if addrFamily == UInt8(AF_INET) {
                let name = String(cString: interface.ifa_name)
                
                // 优先获取WiFi地址(en0)，其次是蜂窝网络(pdp_ip0)
                if name == "en0" || name == "pdp_ip0" {
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface.ifa_addr, socklen_t(interface.ifa_addr.pointee.sa_len),
                               &hostname, socklen_t(hostname.count),
                               nil, socklen_t(0), NI_NUMERICHOST)
                    let ipAddress = String(cString: hostname)
                    
                    // 优先返回WiFi地址
                    if name == "en0" {
                        address = ipAddress
                        break
                    } else if address == nil {
                        address = ipAddress
                    }
                }
            }
        }
        
        freeifaddrs(ifaddr)
        return address
    }
    
    /// 获取公网IP地址（异步）
    static func getPublicIPAddress(completion: @escaping (String?) -> Void) {
        let ipServices = [
            "https://api.ipify.org?format=text",
            "https://ipinfo.io/ip",
            "https://icanhazip.com",
            "https://checkip.amazonaws.com"
        ]
        
        func tryService(at index: Int) {
            guard index < ipServices.count else {
                completion(nil)
                return
            }
            
            guard let url = URL(string: ipServices[index]) else {
                tryService(at: index + 1)
                return
            }
            
            var request = URLRequest(url: url)
            request.timeoutInterval = 5.0
            request.cachePolicy = .reloadIgnoringLocalCacheData
            
            URLSession.shared.dataTask(with: request) { data, response, error in
                if let data = data,
                   let ipString = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines),
                   !ipString.isEmpty,
                   isValidIPAddress(ipString) {
                    DispatchQueue.main.async {
                        completion(ipString)
                    }
                } else {
                    tryService(at: index + 1)
                }
            }.resume()
        }
        
        tryService(at: 0)
    }
    
    /// 验证IP地址格式是否正确
    private static func isValidIPAddress(_ ip: String) -> Bool {
        let parts = ip.components(separatedBy: ".")
        guard parts.count == 4 else { return false }
        
        for part in parts {
            guard let num = Int(part), num >= 0 && num <= 255 else {
                return false
            }
        }
        return true
    }
    
    /// 获取网络类型
    static func getNetworkType() -> String {
        let reachability = SCNetworkReachabilityCreateWithName(nil, "www.apple.com")
        var flags = SCNetworkReachabilityFlags()
        
        guard let reachabilityRef = reachability,
              SCNetworkReachabilityGetFlags(reachabilityRef, &flags) else {
            return "Unknown"
        }
        
        if flags.contains(.reachable) {
            if flags.contains(.isWWAN) {
                return "Cellular"
            } else {
                return "WiFi"
            }
        }
        
        return "No Connection"
    }
    
    /// 检查网络连接状态
    static func isNetworkAvailable() -> Bool {
        let reachability = SCNetworkReachabilityCreateWithName(nil, "www.apple.com")
        var flags = SCNetworkReachabilityFlags()

        guard let reachabilityRef = reachability,
              SCNetworkReachabilityGetFlags(reachabilityRef, &flags) else {
            return false
        }

        return flags.contains(.reachable) && !flags.contains(.connectionRequired)
    }

    /// 获取IP地址的省级归属地信息（带缓存机制）
    static func getIPLocation(completion: @escaping (String?) -> Void) {
        // 先尝试获取公网IP
        getPublicIPAddress { ipAddress in
            guard let ip = ipAddress else {
                completion(nil)
                return
            }

            // 先检查缓存
            if let cachedLocation = IPLocationCache.shared.getCachedLocation(for: ip) {
                DispatchQueue.main.async {
                    completion(cachedLocation)
                }
                return
            }

            // 缓存中没有，进行网络查询
            queryIPLocation(ip: ip) { location in
                if let location = location {
                    // 缓存查询结果
                    IPLocationCache.shared.cacheLocation(location, for: ip)
                }
                completion(location)
            }
        }
    }

    /// 获取IP地址的省级归属地信息（带缓存回调机制）
    /// - Parameters:
    ///   - cacheCompletion: 缓存结果回调（如果有缓存会立即调用）
    ///   - networkCompletion: 网络查询结果回调（无论是否有缓存都会调用）
    static func getIPLocationWithCache(
        cacheCompletion: @escaping (String?) -> Void,
        networkCompletion: @escaping (String?) -> Void
    ) {
        // 先尝试获取公网IP
        getPublicIPAddress { ipAddress in
            guard let ip = ipAddress else {
                DispatchQueue.main.async {
                    cacheCompletion(nil)
                    networkCompletion(nil)
                }
                return
            }

            // 先检查缓存并立即返回
            let cachedLocation = IPLocationCache.shared.getCachedLocation(for: ip)
            DispatchQueue.main.async {
                cacheCompletion(cachedLocation)
            }

            // 无论是否有缓存，都进行网络查询以更新数据
            queryIPLocation(ip: ip) { location in
                if let location = location {
                    // 缓存查询结果
                    IPLocationCache.shared.cacheLocation(location, for: ip)
                }
                DispatchQueue.main.async {
                    networkCompletion(location)
                }
            }
        }
    }

    /// 查询IP地址的归属地信息
    private static func queryIPLocation(ip: String, completion: @escaping (String?) -> Void) {
        // 使用返回中文结果的IP归属地查询服务
        let ipLocationServices = [
            "https://ip-api.com/json/\(ip)?lang=zh-CN&fields=regionName",
            "https://api.vore.top/api/IPdata?ip=\(ip)",
            "https://whois.pconline.com.cn/ipJson.jsp?ip=\(ip)&json=true"
        ]

        func tryService(at index: Int) {
            guard index < ipLocationServices.count else {
                completion(nil)
                return
            }

            guard let url = URL(string: ipLocationServices[index]) else {
                tryService(at: index + 1)
                return
            }

            var request = URLRequest(url: url)
            request.timeoutInterval = 8.0
            request.cachePolicy = .reloadIgnoringLocalCacheData
            request.setValue("application/json", forHTTPHeaderField: "Accept")
            request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15", forHTTPHeaderField: "User-Agent")

            URLSession.shared.dataTask(with: request) { data, response, error in
                if let data = data,
                   let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200 {

                    var province: String?

                    // 尝试解析JSON响应
                    if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        switch index {
                        case 0: // ip-api.com
                            province = json["regionName"] as? String
                        case 1: // api.vore.top
                            if let ipdata = json["ipdata"] as? [String: Any] {
                                province = ipdata["info1"] as? String
                            }
                        case 2: // whois.pconline.com.cn
                            province = json["pro"] as? String
                        default:
                            break
                        }
                    }

                    // 验证省份信息是否有效
                    if let province = province,
                       !province.isEmpty,
                       !province.contains("<"),  // 排除HTML内容
                       !province.contains("DOCTYPE"),  // 排除HTML DOCTYPE
                       !province.contains("Unknown"),  // 排除未知结果
                       province.count < 20 {  // 省份名称不应该太长
                        DispatchQueue.main.async {
                            completion(province)
                        }
                        return
                    }
                }

                // 当前服务失败，尝试下一个
                tryService(at: index + 1)
            }.resume()
        }

        tryService(at: 0)
    }
}
