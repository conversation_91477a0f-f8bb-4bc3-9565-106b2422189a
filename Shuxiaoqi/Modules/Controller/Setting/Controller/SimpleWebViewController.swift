//
//  SimpleWebViewController.swift
//  Shuxiaoqi
//
//  Created by AI Assistant on 2025/7/29.
//
//  简单的WebView测试控制器，用于测试链接点击功能

import UIKit
import WebKit

class SimpleWebViewController: UIViewController {
    
    private var webView: WKWebView!
    private let testURL: URL
    
    init(url: URL) {
        self.testURL = url
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupWebView()
        loadURL()
    }
    
    private func setupUI() {
        view.backgroundColor = .white
        title = "简单WebView测试"

        // 添加关闭按钮
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "关闭",
            style: .plain,
            target: self,
            action: #selector(closeButtonTapped)
        )

        // 添加测试HTML按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "测试HTML",
            style: .plain,
            target: self,
            action: #selector(loadTestHTML)
        )
    }
    
    private func setupWebView() {
        // 创建最简单的WebView配置
        let config = WKWebViewConfiguration()
        
        // 允许内联播放
        config.allowsInlineMediaPlayback = true
        config.mediaTypesRequiringUserActionForPlayback = []
        
        // 创建WebView
        webView = WKWebView(frame: .zero, configuration: config)
        webView.navigationDelegate = self
        webView.translatesAutoresizingMaskIntoConstraints = false
        
        // 添加到视图
        view.addSubview(webView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            webView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        print("🔧 简单WebView创建完成")
        print("🔧 navigationDelegate: \(String(describing: webView.navigationDelegate))")
    }
    
    private func loadURL() {
        let request = URLRequest(url: testURL)
        webView.load(request)
        print("🌐 开始加载URL: \(testURL.absoluteString)")
    }
    
    @objc private func closeButtonTapped() {
        dismiss(animated: true, completion: nil)
    }

    @objc private func loadTestHTML() {
        let testHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>链接测试页面</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; }
                .test-link { display: block; margin: 10px 0; padding: 10px; background: #f0f0f0; text-decoration: none; color: #333; }
                .test-link:hover { background: #e0e0e0; }
            </style>
        </head>
        <body>
            <h1>链接点击测试</h1>
            <p>点击下面的链接测试导航功能：</p>

            <a href="https://www.baidu.com" class="test-link">🔗 百度 (外部链接)</a>
            <a href="https://www.apple.com" class="test-link">🔗 苹果官网 (外部链接)</a>
            <a href="https://gzsxq.com/rule/RuleCenter.html" class="test-link">🔗 规则中心 (原始页面)</a>

            <h2>JavaScript测试</h2>
            <button onclick="testClick()" class="test-link">🔗 JavaScript点击测试</button>

            <script>
                function testClick() {
                    console.log('JavaScript按钮被点击');
                    alert('JavaScript点击测试成功！');
                }

                // 监听所有链接点击
                document.addEventListener('click', function(e) {
                    if (e.target.tagName === 'A') {
                        console.log('链接被点击: ' + e.target.href);
                    }
                });
            </script>
        </body>
        </html>
        """

        webView.loadHTMLString(testHTML, baseURL: nil)
        print("🧪 加载测试HTML页面")
    }
}

// MARK: - WKNavigationDelegate
extension SimpleWebViewController: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        
        guard let url = navigationAction.request.url else {
            print("❌ 导航请求URL为空")
            decisionHandler(.allow)
            return
        }
        
        // 详细的导航类型映射
        let navigationTypeString: String
        switch navigationAction.navigationType {
        case .linkActivated:
            navigationTypeString = "linkActivated (链接点击)"
        case .formSubmitted:
            navigationTypeString = "formSubmitted (表单提交)"
        case .backForward:
            navigationTypeString = "backForward (前进后退)"
        case .reload:
            navigationTypeString = "reload (重新加载)"
        case .formResubmitted:
            navigationTypeString = "formResubmitted (表单重新提交)"
        case .other:
            navigationTypeString = "other (其他)"
        @unknown default:
            navigationTypeString = "unknown (未知)"
        }
        
        print("🔗 [简单WebView] 导航请求:")
        print("   URL: \(url.absoluteString)")
        print("   类型: \(navigationTypeString)")
        print("   目标Frame: \(navigationAction.targetFrame?.isMainFrame == true ? "主框架" : "子框架")")
        
        // 对于链接点击，特别处理
        if navigationAction.navigationType == .linkActivated {
            print("✅ [简单WebView] 检测到链接点击，允许导航")
        }
        
        // 允许所有导航
        decisionHandler(.allow)
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        print("📄 [简单WebView] 页面加载完成: \(webView.url?.absoluteString ?? "未知URL")")
        
        // 注入简单的测试JavaScript
        let testJS = """
        console.log('🔍 [简单WebView] 开始检查页面链接...');
        var links = document.querySelectorAll('a');
        console.log('🔍 [简单WebView] 找到 ' + links.length + ' 个链接');
        
        links.forEach(function(link, index) {
            console.log('🔗 [简单WebView] 链接 ' + index + ': ' + link.href);
        });
        
        // 添加全局点击监听
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                console.log('🖱️ [简单WebView] 链接被点击: ' + e.target.href);
            }
        });
        """
        
        webView.evaluateJavaScript(testJS) { (result, error) in
            if let error = error {
                print("❌ [简单WebView] 注入测试JS失败: \(error)")
            } else {
                print("✅ [简单WebView] 测试JS注入成功")
            }
        }
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        print("❌ [简单WebView] 页面加载失败: \(error.localizedDescription)")
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        print("❌ [简单WebView] 页面预加载失败: \(error.localizedDescription)")
    }
}
