//
//  WebViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/17.
//
//  H5展示页

import UIKit
import WebKit
import CoreLocation
// 请确保您已正确导入 WebViewJavascriptBridge 库，例如:

class WebViewController: BaseViewController, CLLocationManagerDelegate {
    // MARK: - Properties
    private let url: URL
    private var titleText: String?
    private var shouldUseWebPageTitle: Bool = false
    // 是否强制显示导航栏，外部可控制
    private var forceShowNavBar: Bool = false
    // 控制是否显示自定义导航栏（路径初始化的页面不显示）
    private var shouldHideNavigationBar: Bool = false
    // 标记是否通过 path 方式初始化
    private var isPathInitialization: Bool = false
    // 路径初始化且非特殊路径时，页面加载成功后需要隐藏导航栏
    private var hideNavBarAfterLoad: Bool = false
    private var bridge: WebViewJavascriptBridge! // 新增: JSBridge 实例
    
    // 定位相关
    private var locationManager: CLLocationManager?
    private var locationCallbackId: String? // 存储定位回调ID
    private var locationResponseCallback: (([String: Any]) -> Void)? // 存储定位响应回调
    
    // MARK: - UI Components
    private lazy var webView: WKWebView = {
        let config = WKWebViewConfiguration()
        // 配置允许内联播放和其他设置
        config.allowsInlineMediaPlayback = true
        config.mediaTypesRequiringUserActionForPlayback = []

        let webView = WKWebView(frame: .zero, configuration: config)
        // 注意：navigationDelegate 将在 setupJavascriptBridge 中通过 bridge 设置
        // 添加 URL 变化的观察者
        webView.addObserver(self, forKeyPath: #keyPath(WKWebView.url), options: .new, context: nil)
        webView.translatesAutoresizingMaskIntoConstraints = false

        // 启用用户交互
        webView.isUserInteractionEnabled = true

        return webView
    }()
    
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        indicator.translatesAutoresizingMaskIntoConstraints = false
        return indicator
    }()
    
    // MARK: - Initialization
    init(url: URL, title: String? = nil) {
        self.url = url
        self.titleText = title
        self.shouldUseWebPageTitle = (title == nil)
        // 直接传入完整 URL 时，默认显示导航栏
        self.shouldHideNavigationBar = false
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - H5 Base Domain 配置
    /// 默认的 H5 域名，可按需在 App 启动时动态修改。
    static var baseH5Domain: String = {
//        return "http://192.168.10.103:8080/#/"
        return "https://yigou.gzyoushu.com/#/"
    }()
    
    // 特殊路径列表：这些路径即使是用path初始化，也需要显示导航栏
    private static var specialPathsNeedingNavBar: [String] = [
        // 根据用户日志添加不需要原生导航栏的路径（首页需要导航栏）
        "/pages/news/news",
        "/pages/live/live",
        "/pages/goodCart/goodCart",
        "/pages/user/user"
    ]

    // MARK: - Convenience Initializer for H5 Path (仅传入路径)
    /// 仅需传入 H5 页面路径，域名由 `baseH5Domain` 统一配置。
    /// - Parameters:
    ///   - path: H5 页面路径，例如 "/pages/order/index"。
    ///   - title: 可选页面标题。
    convenience init(path: String, title: String? = nil) {
        var normalizedPath = path
        // 移除自动添加的斜杠，因为域名已经包含了 "#/"
        if normalizedPath.hasPrefix("/") { normalizedPath = String(normalizedPath.dropFirst()) }

        let fullURLString = WebViewController.baseH5Domain + normalizedPath
        guard let completeURL = URL(string: fullURLString) else {
            fatalError("无法构建有效的 URL：\(fullURLString)")
        }
        self.init(url: completeURL, title: title)
        
        // 路径初始化默认先显示导航栏，防止加载失败无法返回
        self.shouldHideNavigationBar = false
        self.isPathInitialization = true
        
        // 检查是否是特殊路径
        let isSpecialPath = WebViewController.specialPathsNeedingNavBar.contains { specialPath in
            normalizedPath == specialPath || normalizedPath.hasPrefix(specialPath + "/")
        }
        
        // 非特殊路径，加载成功后需要隐藏导航栏
        self.hideNavBarAfterLoad = !isSpecialPath
    }
    
    // MARK: - 外部控制导航栏
    
    /// 使用此方法强制显示导航栏，不管URL是什么
    /// - Parameter show: 是否显示导航栏
    func forceNavigationBarVisibility(show: Bool) {
        self.forceShowNavBar = show
        self.shouldHideNavigationBar = !show
        updateNavigationBarVisibility()
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始根据 shouldHideNavigationBar 设置 BaseViewController 导航栏显示
        showNavBar = !shouldHideNavigationBar

        // 设置标题
        if let title = titleText {
            navTitle = title
        } else {
            // 尝试从 URL 提取页面类型作为标题
            let extractedTitle = extractTitleFromURL(url)
            navTitle = extractedTitle ?? "加载中..."
        }

        // 添加测试按钮（仅在规则中心页面显示）
        if url.absoluteString.contains("gzsxq.com/rule/RuleCenter.html") {
            navigationItem.rightBarButtonItem = UIBarButtonItem(
                title: "简单测试",
                style: .plain,
                target: self,
                action: #selector(openSimpleWebView)
            )
        }

        // 设置内容视图
        // 监听登录成功后的 Token 保存通知
        NotificationCenter.default.addObserver(self, selector: #selector(handleTokenSaved(_:)), name: .tokenSaved, object: nil)

        setupUI()
        setupJavascriptBridge() // 新增: 初始化JSBridge
        setupURLMonitoring()
        loadWebContent()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 添加 WebView 到内容视图
        contentView.addSubview(webView)
        view.addSubview(loadingIndicator)
        
        // 让 WebView 填充整个内容视图
        webView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: contentView.topAnchor),
            webView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            loadingIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor),
        ])
    }
    
    // MARK: - JS Bridge Setup (新增部分)
    private func setupJavascriptBridge() {
        WebViewJavascriptBridge.enableLogging()

        // 创建桥接实例
        bridge = WebViewJavascriptBridge(webView)
        bridge.setWebViewDelegate(self)

        print("🌉 创建的Bridge类型: \(type(of: bridge))")
        print("🌉 WebView的navigationDelegate: \(String(describing: webView.navigationDelegate))")
        print("🌉 Bridge设置完成，WebViewController已设置为Bridge的delegate")
        
        // 注册一个供H5调用的handler示例： "nativeAction"
        bridge.registerHandler("nativeAction") { [weak self] (data, responseCallback) in
            print("nativeAction called by JS with data: \(data ?? "nil")")
            responseCallback?("Response from Swift: nativeAction executed")
        }
        
        // 注册demo中的 "changeUser" handler
        bridge.registerHandler("changeUser") { [weak self] (data, responseCallback) in
            if let userName = data as? String {
                print("changeUser called from JS with userName: \(userName)")
                self?.titleLabel.text = "User: \(userName)"
            }
            responseCallback?("User changed successfully by Swift")
        }
        
        // 注册拨打电话的handler
        bridge.registerHandler("goCallPhone") { [weak self] (data, responseCallback) in
            guard let self = self else { return }
            
            // 打印原始数据，帮助调试
            print("goCallPhone接收到的原始数据: \(data ?? "nil")")
            
            // 获取电话号码
            var phoneNumber: String = ""
            
            // 数据可能直接是字典
            if let dataDict = data as? [String: Any] {
                if let phone = dataDict["phone"] {
                    // 直接在第一层找到phone
                    phoneNumber = "\(phone)"
                    print("直接获取到电话号码: \(phoneNumber)")
                } else if let nestedData = dataDict["data"] as? [String: Any], let phone = nestedData["phone"] {
                    // 在嵌套的data字段中找到phone
                    phoneNumber = "\(phone)"
                    print("从嵌套data中获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 如果还是没获取到，尝试将整个数据转为字符串查看
            if phoneNumber.isEmpty {
                print("未能解析电话号码，原始数据结构: \(String(describing: data))")
                
                // 尝试其他可能的数据格式
                if let phoneString = data as? String, !phoneString.isEmpty {
                    phoneNumber = phoneString
                    print("从字符串中获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 确保电话号码有效
            if phoneNumber.isEmpty {
                print("最终未获取到电话号码")
                // 使用标准格式返回失败响应
                responseCallback?(["data": NSNull(), "status": 0, "msg": "未提供电话号码或格式不正确"])
                return
            }
            
            // 拨打电话
            if let url = URL(string: "tel://\(phoneNumber)") {
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            if success {
                                print("成功显示拨打电话界面: \(phoneNumber)")
                                // 使用标准格式返回成功响应
                                let resultData: [String: Any] = ["phone": phoneNumber]
                                responseCallback?(["data": resultData, "status": 1, "msg": "已显示拨打电话界面"])
                            } else {
                                print("无法显示拨打电话界面")
                                // 使用标准格式返回失败响应
                                responseCallback?(["data": NSNull(), "status": 0, "msg": "无法显示拨打电话界面"])
                            }
                        }
                    } else {
                        print("设备不支持拨打电话功能")
                        self.showAlert(message: "无法拨打电话")
                        // 使用标准格式返回失败响应
                        responseCallback?(["data": NSNull(), "status": 0, "msg": "设备不支持拨打电话功能"])
                    }
                }
            } else {
                // 使用标准格式返回失败响应
                responseCallback?(["data": NSNull(), "status": 0, "msg": "无效的电话号码格式"])
            }
        }
        
        // 注册获取用户 Token 的 handler（常规 WVJB 回调）
        bridge.registerHandler("goGetToken") { [weak self] (data, responseCallback) in
            guard let self = self else { return }
            
            // 使用新方法发送 token 响应
            self.sendTokenResponse(responseCallback: responseCallback)
            
            // 打印调试信息
            print("goGetToken 已处理，返回简化 token 格式")
        }
        
        // 注册 goLogin 处理程序
        bridge.registerHandler("goLogin") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("[WebViewController] goLogin called, presenting native login page…")

            // 跳转到原生登录页面
            let loginVC = LoginViewController2_0()
            loginVC.modalPresentationStyle = .fullScreen
            self.present(loginVC, animated: true, completion: nil)

            // 登录完成后 token 将通过 Notification .tokenSaved 回传，由 handleTokenSaved 统一处理
        }
        
        // 注册获取位置的handler
        bridge.registerHandler("goLocation") { [weak self] (data, responseCallback) in
            guard let self = self else { return }
            
            print("【注册处理】goLocation接收到的原始数据: \(data ?? "nil")")
            
            // 保存回调函数
            self.locationCallbackId = nil // 清除之前的回调ID
            
            // 创建一个自定义回调函数，用于通过responseCallback返回结果
            let locationResponseCallback: (([String: Any]) -> Void)? = { responseData in
                print("【注册处理】goLocation返回数据: \(responseData)")
                responseCallback?(responseData)
            }
            
            // 初始化定位管理器
            if self.locationManager == nil {
                self.locationManager = CLLocationManager()
                self.locationManager?.delegate = self
                self.locationManager?.desiredAccuracy = kCLLocationAccuracyBest
                print("【注册处理】初始化定位管理器")
            } else {
                print("【注册处理】使用现有定位管理器")
            }
            
            // 检查定位权限
            let authStatus = CLLocationManager.authorizationStatus()
            if authStatus == .notDetermined {
                // 请求定位权限
                print("【注册处理】请求定位权限")
                self.locationManager?.requestWhenInUseAuthorization()
                // 保存回调以便权限授予后使用
                self.locationResponseCallback = locationResponseCallback
            } else if authStatus == .denied || authStatus == .restricted {
                // 用户拒绝定位权限
                print("【注册处理】定位权限被拒绝")
                self.showAlert(message: "请在设置中开启定位权限")
                locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位权限被拒绝"])
            } else {
                // 保存回调
                print("【注册处理】已获得定位权限，开始定位")
                self.locationResponseCallback = locationResponseCallback
                // 开始定位
                self.locationManager?.startUpdatingLocation()
            }
        }
        
        // 设置处理未捕获消息的回调
        bridge.setUnhandledMessageCallback { [weak self] (bridge, message) in
            guard let self = self else { return }
            
            // 打印未捕获的消息
            if let messageDict = message as? [String: Any] {
                print("【WebViewController】未处理的JS消息: \(messageDict)")
                
                // 获取处理程序名称
                if let handlerName = messageDict["handlerName"] as? String {
                    print("【WebViewController】JS尝试调用未注册的处理程序: \(handlerName)")
                    
                    // 获取数据
                    if let data = messageDict["data"] {
                        print("【WebViewController】JS传递的数据: \(data)")
                        
                        // 在这里可以根据handlerName进行自定义处理
                        self.handleUnregisteredJSHandler(handlerName: handlerName, data: message)
                    }
                    
                    // 如果有回调ID，可以向JS返回一个错误消息
                    if let callbackId = messageDict["callbackId"] as? String {
                        // 使用标准格式返回失败响应
                        self.sendFailureToJS(callbackId: callbackId, msg: "未找到处理程序: \(handlerName)")
                    }
                }
            }
        }
    }
    
    // 处理未注册的JS处理程序
    private func handleUnregisteredJSHandler(handlerName: String, data: Any) {
        // 这里可以根据handlerName来决定如何处理未注册的JS调用
        switch handlerName {
        case "goScanCode":
            //打开扫码页面
            let scanVC = QRScanViewController()
            self.navigationController?.pushViewController(scanVC, animated: true)
        case "goOpenShare":
            print("打开分享页面")
            
        case "goCallPhone":
            // 打印原始数据，帮助调试
            print("未注册处理程序捕获goCallPhone，原始数据: \(data)")
            
            // 获取电话号码和回调ID
            var phoneNumber: String = ""
            var callbackId: String? = nil
            
            if let messageDict = data as? [String: Any] {
                // 获取回调ID
                if let cbId = messageDict["callbackId"] as? String {
                    callbackId = cbId
                }
                
                // 尝试获取数据中的电话号码 - 可能在data字段嵌套
                if let jsonData = messageDict["data"] as? [String: Any] {
                    if let phone = jsonData["phone"] {
                        // 将phone转换为字符串
                        phoneNumber = "\(phone)"
                        print("从嵌套data中获取到电话号码: \(phoneNumber)")
                    }
                }
                
                // 如果在data中没找到，尝试直接在第一层找
                if phoneNumber.isEmpty, let phone = messageDict["phone"] {
                    phoneNumber = "\(phone)"
                    print("直接获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 确保电话号码有效
            if phoneNumber.isEmpty {
                print("最终未获取到电话号码")
                if let callbackId = callbackId {
                    // 使用标准格式返回失败响应
                    sendFailureToJS(callbackId: callbackId, msg: "未提供电话号码或格式不正确")
                }
                return
            }
            
            // 拨打电话
            if let url = URL(string: "tel://\(phoneNumber)") {
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            if success {
                                print("成功显示拨打电话界面: \(phoneNumber)")
                                
                                // 如果有回调ID，返回显示结果
                                if let callbackId = callbackId {
                                    // 使用标准格式返回成功响应
                                    let resultData: [String: Any] = ["phone": phoneNumber]
                                    self.sendSuccessToJS(callbackId: callbackId, data: resultData, msg: "已显示拨打电话界面")
                                }
                            } else {
                                print("无法显示拨打电话界面")
                                
                                // 如果有回调ID，返回错误结果
                                if let callbackId = callbackId {
                                    // 使用标准格式返回失败响应
                                    self.sendFailureToJS(callbackId: callbackId, msg: "无法显示拨打电话界面")
                                }
                            }
                        }
                    } else {
                        print("设备不支持拨打电话功能")
                        // 可以显示一个提示
                        self.showAlert(message: "无法拨打电话")
                        
                        // 如果有回调ID，返回错误信息
                        if let callbackId = callbackId {
                            // 使用标准格式返回失败响应
                            self.sendFailureToJS(callbackId: callbackId, msg: "设备不支持拨打电话功能")
                        }
                    }
                }
            }
            
        case "getAppInfo":
            // 示例：如果JS尝试获取应用信息，我们可以在这里动态处理
            let appInfo = ["appName": "Shuxiaoqi", "version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""]
            print("应用信息已准备: \(appInfo)")
            // 如果需要，您可以将这些信息传回JS
            
        case "navigate":
            // 示例：处理导航请求
            if let navigationData = data as? [String: Any], let page = navigationData["page"] as? String {
                print("收到导航请求到: \(page)")
                // 在这里可以执行相应的导航逻辑
            }
            
        case "goBackPrevPage":
            self.dismiss(animated: true)
            self.navigationController?.popViewController(animated: true)
            
        case "goLogin":
            // 处理登录请求
            print("捕获到goLogin未注册处理")
            var callbackId: String? = nil
            
            // 获取回调ID
            if let messageDict = data as? [String: Any], let cbId = messageDict["callbackId"] as? String {
                callbackId = cbId
            }
            
            // 使用新方法发送 token 响应
            self.sendTokenResponse(responseCallback: { (response) in
                if let responseDict = response as? [String: Any], let status = responseDict["status"] as? Int {
                    if status == 1 {
                        self.sendSuccessToJS(callbackId: callbackId ?? "", data: responseDict["data"] as Any, msg: responseDict["msg"] as? String ?? "操作成功")
                    } else {
                        self.sendFailureToJS(callbackId: callbackId ?? "", msg: responseDict["msg"] as? String ?? "操作失败", data: responseDict["data"] as Any)
                    }
                }
            })
            
            // 打印调试信息
            print("goLogin 未注册处理已完成，返回简化 token 格式")
            
        default:
            print("未知的处理程序: \(handlerName)，暂不处理")
        }
    }
    
    // 向JS发送错误响应
    private func sendErrorResponseToJS(callbackId: String, data: Any) {
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": data
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送错误响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 向JS发送成功响应
    private func sendSuccessResponseToJS(callbackId: String, data: Any) {
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": data
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送成功响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 示例：调用H5的JS方法 (新增部分)
    // 您可以在需要的时候调用此方法，例如用户点击某个按钮
    func sendMessageToJavaScript(handlerName: String, data: Any?) {
        bridge.callHandler(handlerName, data: data) { (responseData) in
            print("\(handlerName) JS handler response: \(responseData ?? "nil")")
            // 处理来自JS的响应
            // if let responseDict = responseData as? [String: Any], let status = responseDict["status"] as? String {
            //     self?.showAlert(message: "JS response status: \(status)")
            // }
        }
    }
    
    func flushMessageQueue() {
//        bridge.flatMap { responseData in
//            printf(responseData)
//        }
    }
    
    // 示例：调用demo中的 "changeName"
    func callChangeNameInJS(name: String) {
        sendMessageToJavaScript(handlerName: "changeName", data: name)
    }
    
    private func loadWebContent() {
        loadingIndicator.startAnimating()
        
        // 获取公参
        let commonParams = UserDefaults.standard.string(forKey: "commonParams") ?? ""
        
        // 构建URL请求
        if var urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false) {
            var queryItems = urlComponents.queryItems ?? []
            
            // 添加Token
            if let token = UserDefaults.standard.string(forKey: "userToken") {
                queryItems.append(URLQueryItem(name: "token", value: token))
            }
            
            // 添加公参
            if !commonParams.isEmpty {
                let commonParamsItems = commonParams.components(separatedBy: "&")
                for param in commonParamsItems {
                    let components = param.components(separatedBy: "=")
                    if components.count == 2 {
                        queryItems.append(URLQueryItem(name: components[0], value: components[1]))
                    }
                }
            }
            
            urlComponents.queryItems = queryItems
            
            if let updatedURL = urlComponents.url {
                let request = URLRequest(url: updatedURL)
                webView.load(request)
                return
            }
        }
        
        // 如果URL构建失败，使用原始URL
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    // 向JS发送标准格式的响应
    private func sendStandardResponseToJS(callbackId: String, data: Any? = nil, status: Int = 1, msg: String = "") {
        // 创建符合规范的响应数据
        let standardData: [String: Any] = [
            "data": data ?? NSNull(),
            "status": status,  // 0: 调用失败, 1: 调用成功
            "msg": msg
        ]
        
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": standardData
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送标准响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 向JS发送成功响应（标准格式）
    private func sendSuccessToJS(callbackId: String, data: Any? = nil, msg: String = "操作成功") {
        sendStandardResponseToJS(callbackId: callbackId, data: data, status: 1, msg: msg)
    }
    
    // 向JS发送失败响应（标准格式）
    private func sendFailureToJS(callbackId: String, msg: String = "操作失败", data: Any? = nil) {
        sendStandardResponseToJS(callbackId: callbackId, data: data, status: 0, msg: msg)
    }

    // 直接发送响应数据到JS，不使用外层包装
    private func sendDirectResponseToJS(callbackId: String, responseData: [String: Any]) {
        // 使用WebViewJavascriptBridge的原始方法直接发送响应数据
        let javascriptCommand = """
        WebViewJavascriptBridge._handleMessageFromObjC('{"responseId":"\(callbackId)","responseData":\(self.jsonStringFromDictionary(responseData))}');
        """
        
        // 在主线程上执行JavaScript
        DispatchQueue.main.async {
            self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                if let error = error {
                    print("直接发送响应到JS失败: \(error)")
                }
            })
        }
    }
    
    // 将字典转换为JSON字符串
    private func jsonStringFromDictionary(_ dict: [String: Any]) -> String {
        if let jsonData = try? JSONSerialization.data(withJSONObject: dict, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            return jsonString
        }
        return "{}" // 失败时返回空对象
    }

    // MARK: - URL 变化监控
    
    // 注册 JS 回调来监听 URL 变化
    private func setupURLMonitoring() {
        // 注入 JavaScript 监听 hashchange 和 popstate 事件
        let script = """
        function notifyURLChange() {
            window.webkit.messageHandlers.urlChangeObserver.postMessage({
                url: window.location.href,
                path: window.location.pathname + window.location.hash
            });
        }
        
        // 监听 hashchange 事件（用于 hash 路由变化，如 /#/page1 -> /#/page2）
        window.addEventListener('hashchange', notifyURLChange);
        
        // 监听 popstate 事件（用于 history API 路由变化）
        window.addEventListener('popstate', notifyURLChange);
        
        // 对 history.pushState 和 replaceState 进行重写，以便捕获 SPA 路由变化
        (function() {
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function() {
                originalPushState.apply(this, arguments);
                notifyURLChange();
            };
            
            history.replaceState = function() {
                originalReplaceState.apply(this, arguments);
                notifyURLChange();
            };
        })();
        
        // 初始通知
        notifyURLChange();
        """
        
        // 创建用户脚本
        let userScript = WKUserScript(source: script, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        webView.configuration.userContentController.addUserScript(userScript)
        
        // 添加消息处理程序
        webView.configuration.userContentController.add(self, name: "urlChangeObserver")
    }
    
    // KVO 观察 webView.url 变化
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == #keyPath(WKWebView.url) {
            if let url = webView.url {
                print("WebView URL 变化 (KVO): \(url.absoluteString)")
                checkAndUpdateNavBarForSpecialPaths(urlString: url.absoluteString)
            }
            // 已处理，无需调用 super，避免未处理异常
            return
        }
        super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
    }
    
    // 检查是否为需要导航栏的特殊路径
    private func checkAndUpdateNavBarForSpecialPaths(urlString: String) {
        // 解析 URL 和路径（包括 hash 部分）
        guard let url = URL(string: urlString) else {
            print("无法解析URL: \(urlString)")
            return
        }
        
        // 如果 URL 域名与 baseH5Domain 不同，则始终显示原生导航栏
        if let baseURL = URL(string: WebViewController.baseH5Domain), url.host != baseURL.host {
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }

        // 如果外部已强制设置导航栏可见性，则不再自动调整
        if forceShowNavBar {
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }
        
        // 首页判断 - 首页需要显示导航栏
        let isHomePage = isHomeURL(url)
        if isHomePage {
            print("检测到首页URL，显示导航栏")
            // 设置首页标题
            if navTitle != "首页" {
                navTitle = "首页"
            }
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }
        
        // 对于 SPA，检查 hash 部分 (#/xxx)
        var pathToCheck = url.path
        if let fragment = url.fragment, !fragment.isEmpty {
            // 处理 fragment 中可能包含的查询参数
            let fragmentComponents = fragment.components(separatedBy: "?")
            var cleanFragment = fragmentComponents[0]
            
            // 移除开头的 #/
            if cleanFragment.hasPrefix("#") {
                cleanFragment = String(cleanFragment.dropFirst())
            }
            if cleanFragment.hasPrefix("/") {
                cleanFragment = String(cleanFragment.dropFirst())
            }
            
            pathToCheck = "/" + cleanFragment
        }
        
        print("解析后的路径: \(pathToCheck)")
        
        // 检查是否在特殊路径列表中
        let needsNavBar = WebViewController.specialPathsNeedingNavBar.contains { specialPath in
            let comparePath = pathToCheck.trimmingCharacters(in: CharacterSet(charactersIn: "/"))
            let compareSpecial = specialPath.trimmingCharacters(in: CharacterSet(charactersIn: "/"))
            return comparePath == compareSpecial || comparePath.hasPrefix(compareSpecial + "/")
        }

        print("路径[\(pathToCheck)]是否需要导航栏: \(needsNavBar)")
        
        // 如果当前路径状态与导航栏状态不匹配，则更新导航栏
        if needsNavBar == shouldHideNavigationBar {
            shouldHideNavigationBar = !needsNavBar
            print("更新导航栏可见性: \(needsNavBar ? "显示" : "隐藏")")
            updateNavigationBarVisibility()
        }

        // 动态更新标题（URL 变化时）
        if let dynamicTitle = extractTitleFromURL(url) {
            if dynamicTitle != navTitle {
                DispatchQueue.main.async { [weak self] in
                    self?.navTitle = dynamicTitle
                }
            }
        }
    }
    
    private func updateNavigationBarVisibility() {
        // 直接使用 BaseViewController 的导航栏控制
        showNavBar = !shouldHideNavigationBar
    }
    
    // MARK: - Test Methods
    @objc private func openSimpleWebView() {
        print("🧪 打开简单WebView测试")
        let simpleWebVC = SimpleWebViewController(url: url)
        let navController = UINavigationController(rootViewController: simpleWebVC)
        present(navController, animated: true, completion: nil)
    }

    // MARK: - Notification Handling
    @objc private func handleTokenSaved(_ notification: Notification) {
        guard let token = notification.userInfo?["token"] as? String, !token.isEmpty else { return }
        print("[WebViewController] Received tokenSaved notification, sending token to JS via onSaveToken")
        bridge.callHandler("onSaveToken", data: ["token": token], responseCallback: nil)
    }

    // 清理工作
    deinit {
        NotificationCenter.default.removeObserver(self, name: .tokenSaved, object: nil)
        // 移除 URL 观察者
        webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.url))
        // 移除消息处理程序
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "urlChangeObserver")
    }
    
    /* --------------- 以下调试方法已弃用 ---------------
    private func debugBridgeState() {
        // 调试代码已移除
    }
    -------------------------------------------------- */
    
    // 判断URL是否为首页
    private func isHomeURL(_ url: URL) -> Bool {
        // 检查是否是基础域名（没有路径或只有/?或/?#或/?#/）
        // 从 baseH5Domain 中提取出主机部分
        guard let baseURL = URL(string: WebViewController.baseH5Domain) else { return false }
        
        // 检查主机部分是否匹配
        guard url.host == baseURL.host else { return false }
        
        // 检查路径部分
        let path = url.path.trimmingCharacters(in: .whitespacesAndNewlines)
        if path != "" && path != "/" {
            return false
        }
        
        // 检查片段部分
        if let fragment = url.fragment {
            // 如果片段非空，检查是否为空路径
            var cleanFragment = fragment.trimmingCharacters(in: CharacterSet(charactersIn: "#/"))
            
            // 检查是否有查询参数
            if cleanFragment.contains("?") {
                cleanFragment = cleanFragment.components(separatedBy: "?")[0]
            }
            
            // 空路径或仅包含"?"表示是首页
            return cleanFragment.isEmpty || cleanFragment == "?"
        }
        
        // 没有片段，没有特殊路径 - 认为是首页
        return true
    }

    // MARK: - 标题处理
    
    /// 从 URL 路径中提取标题
    private func extractTitleFromURL(_ url: URL) -> String? {
        // 获取 URL 的路径部分或片段（针对 SPA）
        var pathToCheck: String
        
        if let fragment = url.fragment, !fragment.isEmpty {
            // 处理片段中可能包含的查询参数
            let fragmentComponents = fragment.components(separatedBy: "?")
            pathToCheck = fragmentComponents[0]
            
            // 移除开头的 #/
            if pathToCheck.hasPrefix("#") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
            if pathToCheck.hasPrefix("/") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
        } else {
            pathToCheck = url.path.trimmingCharacters(in: .whitespacesAndNewlines)
            if pathToCheck.hasPrefix("/") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
        }
        
        // 检查是否是首页
        if pathToCheck.isEmpty || pathToCheck == "/" {
            return "首页"
        }
        
        // 页面路径与标题映射
        let pathTitleMap: [String: String] = [
            "pages/news/news": "新闻资讯",
            "pages/live/live": "直播",
            "pages/goodCart/goodCart": "购物车",
            "pages/user/user": "个人中心",
            "goodPage/goodDetail": "商品详情",
            "pages/goodDetail": "商品详情"
        ]
        
        // 尝试精确匹配
        if let title = pathTitleMap[pathToCheck] {
            return title
        }
        
        // 尝试前缀匹配
        for (path, title) in pathTitleMap {
            if pathToCheck.hasPrefix(path) {
                return title
            }
        }
        
        // 尝试基于目录结构分析
        let components = pathToCheck.components(separatedBy: "/")
        if components.count >= 2 {
            // 检查最后一个组件是否为页面类型标识
            let lastComponent = components.last ?? ""
            
            // 常见页面类型映射
            let pageTypeMap: [String: String] = [
                "list": "列表",
                "detail": "详情",
                "edit": "编辑",
                "profile": "个人信息",
                "settings": "设置",
                "search": "搜索",
                "cart": "购物车",
                "order": "订单",
                "live": "直播",
                "news": "新闻",
                "user": "用户中心"
            ]
            
            // 匹配常见页面类型
            for (type, title) in pageTypeMap {
                if lastComponent.contains(type) {
                    // 如果有上级目录，尝试加上上级目录名称
                    if components.count > 2 {
                        let parentDir = components[components.count - 2]
                        return "\(parentDir)\(title)"
                    }
                    return title
                }
            }
            
            // 如果没有特定匹配，返回最后一个路径组件作为标题
            return lastComponent.capitalized
        }
        
        // 无法识别的路径，返回 nil 以使用默认值
        return nil
    }

    // 创建一个特定用于 token 响应的方法，尝试不同的格式来匹配 H5 端的期望
    private func sendTokenResponse(callbackId: String? = nil, responseCallback: ((Any?) -> Void)? = nil) {
        // 从本地（UserDefaults）获取 token
        let token = UserDefaults.standard.string(forKey: "userToken") ?? ""

        // 如果未获取到 token，则返回失败状态 0
        let isTokenAvailable = !token.isEmpty
        let response: [String: Any] = [
            "data": isTokenAvailable ? ["token": token] : NSNull(),
            "errMsg": isTokenAvailable ? NSNull() : "未找到本地 token",
            "msg": isTokenAvailable ? "成功" : "失败",
            "status": isTokenAvailable ? 1 : 0
        ]

        // DEBUG: 打印即将发送给 H5 的 token 响应内容
        print("[WebViewController] sendTokenResponse -> outgoing response: \(response)")
        
        if let callback = responseCallback {
            // 使用正常回调方式
            callback(response)
        } else if let cbId = callbackId {
            // 使用扁平结构的 JSON（去掉 responseData 包裹，直接平级返回）
            let flatResponse: [String: Any] = [
                "responseId": cbId,
                "data": isTokenAvailable ? ["token": token] : NSNull(),
                "errMsg": isTokenAvailable ? NSNull() : "未找到本地 token",
                "msg": isTokenAvailable ? "成功" : "失败",
                "status": isTokenAvailable ? 1 : 0
            ]

            // DEBUG: 打印扁平结构的响应，方便排查 JS 端回调内容
            print("[WebViewController] sendTokenResponse -> flatResponse: \(flatResponse)")
            
            let jsonString = jsonStringFromDictionary(flatResponse)
            let jsCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString)');"
            
            DispatchQueue.main.async { [weak self] in
                self?.webView.evaluateJavaScript(jsCommand, completionHandler: { (_, error) in
                    if let error = error {
                        print("Token响应发送失败: \(error)")
                    } else {
                        print("Token响应已发送 (扁平结构)")
                    }
                })
            }
        }
    }

    /*
     MARK: - 示例方法：responseData 中返回 JSON 字符串
     说明：当 H5 端只能解析 responseData 为 JSON 字符串时，可参考此实现。
    */
    /*
    private func sendTokenResponseAsJSONString(callbackId: String) {
        let tokenPayload: [String: Any] = [
            "data": ["token": "<Your_Token_Here>"],
            "errMsg": NSNull(),
            "msg": "成功",
            "status": 1
        ]
        guard let payloadJSON = jsonStringFromDictionary(tokenPayload) else { return }
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            // 注意：这里的 responseData 传入 JSON 字符串
            "responseData": payloadJSON
        ]
        let messageJSON = jsonStringFromDictionary(responseMessage)
        let jsCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(messageJSON)');"
        DispatchQueue.main.async { [weak self] in
            self?.webView.evaluateJavaScript(jsCommand, completionHandler: nil)
        }
    }
    */
    
    /* --------------- 以下测试按钮注入方法已弃用 ---------------
    private func injectTestButton() {
        // 测试代码已移除
    }
    -------------------------------------------------------- */
}

// MARK: - WebView 缓存清理
extension WebViewController {
    /// 退出登录时调用，清理 WKWebView 的 Cookies 与网站数据，避免残留登录状态。
    /// - Parameter completion: 清理完成后的回调，可选。
    static func clearLoginCache(completion: (() -> Void)? = nil) {
        // 1. 清理共享 HTTPCookieStorage 中的 Cookie
        if let cookies = HTTPCookieStorage.shared.cookies {
            for cookie in cookies {
                HTTPCookieStorage.shared.deleteCookie(cookie)
            }
        }

        // 2. 清理 WKWebsiteDataStore 中的数据（包括 LocalStorage / IndexedDB 等）
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        // 选择足够早的时间以删除所有记录
        let sinceDate = Date(timeIntervalSince1970: 0)
        dataStore.removeData(ofTypes: dataTypes, modifiedSince: sinceDate) {
            print("[WebViewController] WebView 登录缓存已清理")
            completion?()
        }
    }
}

// MARK: - WKNavigationDelegate
extension WebViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        loadingIndicator.stopAnimating()

        // 打印当前页面URL，方便确认特殊路径
        if let currentURL = webView.url {
            print("📄 WebView页面加载完成:")
            print("   URL: \(currentURL.absoluteString)")
            print("   路径: \(currentURL.path)")
            print("   navigationDelegate: \(String(describing: webView.navigationDelegate))")

            // 如果 titleText 为空，尝试从路径提取标题
            if shouldUseWebPageTitle && titleText == nil {
                if let extractedTitle = extractTitleFromURL(currentURL) {
                    self.navTitle = extractedTitle
                }
            }

            // 注入测试JavaScript来检查链接点击
            let testJS = """
            console.log('🔍 开始检查页面链接...');
            var links = document.querySelectorAll('a');
            console.log('🔍 找到 ' + links.length + ' 个链接');

            // 检查所有可点击元素
            var clickableElements = document.querySelectorAll('[onclick], [data-href], .clickable, button');
            console.log('🔍 找到 ' + clickableElements.length + ' 个可点击元素');

            // 为所有链接添加点击事件监听
            links.forEach(function(link, index) {
                console.log('🔗 链接 ' + index + ': href=' + link.href + ', onclick=' + link.onclick);

                // 移除可能阻止默认行为的事件监听器
                var newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);

                newLink.addEventListener('click', function(e) {
                    console.log('🖱️ 链接被点击: ' + this.href);
                    // 不阻止默认行为，让原生处理
                });
            });

            // 检查是否有阻止链接点击的全局事件监听器
            document.addEventListener('click', function(e) {
                console.log('🖱️ 全局点击事件:', e.target.tagName, e.target.href || e.target.getAttribute('data-href'));
            });
            """

            webView.evaluateJavaScript(testJS) { (result, error) in
                if let error = error {
                    print("❌ 注入测试JS失败: \(error)")
                } else {
                    print("✅ 测试JS注入成功")
                }
            }
        }

        // 如果需要在加载完成后隐藏导航栏（仅路径初始化且非特殊路径）
        if hideNavBarAfterLoad && !shouldHideNavigationBar {
            shouldHideNavigationBar = true
            updateNavigationBarVisibility()
        }

        // 如果没有传入标题，则使用网页的标题
        if shouldUseWebPageTitle {
            webView.evaluateJavaScript("document.title") { [weak self] (result, error) in
                if let title = result as? String, !title.isEmpty {
                    DispatchQueue.main.async {
                        self?.navTitle = title
                    }
                }
            }
        }
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        loadingIndicator.stopAnimating()
        // 处理加载失败
        handleLoadError(error)
    }

    // 新增：首屏加载失败（包括网络不可达等）
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        loadingIndicator.stopAnimating()
        handleLoadError(error)
    }
    
    // 处理导航决策，支持链接点击跳转
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        // 获取请求的URL
        guard let url = navigationAction.request.url else {
            print("❌ 导航请求URL为空")
            decisionHandler(.allow)
            return
        }

        // 详细的导航类型映射
        let navigationTypeString: String
        switch navigationAction.navigationType {
        case .linkActivated:
            navigationTypeString = "linkActivated (链接点击)"
        case .formSubmitted:
            navigationTypeString = "formSubmitted (表单提交)"
        case .backForward:
            navigationTypeString = "backForward (前进后退)"
        case .reload:
            navigationTypeString = "reload (重新加载)"
        case .formResubmitted:
            navigationTypeString = "formResubmitted (表单重新提交)"
        case .other:
            navigationTypeString = "other (其他)"
        @unknown default:
            navigationTypeString = "unknown (未知)"
        }

        print("🔗 WebView导航请求:")
        print("   URL: \(url.absoluteString)")
        print("   类型: \(navigationTypeString)")
        print("   目标Frame: \(navigationAction.targetFrame?.isMainFrame == true ? "主框架" : "子框架")")

        // 对于链接点击，特别处理
        if navigationAction.navigationType == .linkActivated {
            print("✅ 检测到链接点击，允许导航")
            decisionHandler(.allow)
            return
        }

        // 对于其他类型的导航，也允许
        print("✅ 允许导航请求")
        decisionHandler(.allow)
    }
}

// MARK: - CLLocationManagerDelegate
extension WebViewController {
    // 位置更新回调
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // 停止定位，节省电量
        locationManager?.stopUpdatingLocation()
        print("【定位回调】已停止定位更新")
        
        let latitude = location.coordinate.latitude
        let longitude = location.coordinate.longitude
        
        print("【定位回调】获取到位置: 纬度 \(latitude), 经度 \(longitude)")
        
        // 获取详细地址
        print("【定位回调】开始地理编码获取详细地址")
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] (placemarks, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("【定位回调】地理编码失败: \(error)")
                // 使用回调返回错误
                self.locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "获取地址失败"])
                return
            }
            
            guard let placemark = placemarks?.first else {
                print("【定位回调】未找到地标信息")
                // 虽然没有详细地址，但有经纬度，仍然返回成功
                let locationData: [String: String] = [
                    "lat": "\(latitude)",
                    "lng": "\(longitude)",
                    "address": ""
                ]
                print("【定位回调】返回位置数据(无详细地址): \(locationData)")
                self.locationResponseCallback?(["data": locationData, "status": 1, "msg": "获取位置成功(无详细地址)"])
                return
            }
            
            // 构建详细地址
            var addressComponents: [String] = []
            
            if let country = placemark.country { addressComponents.append(country) }
            if let administrativeArea = placemark.administrativeArea { addressComponents.append(administrativeArea) }
            if let locality = placemark.locality { addressComponents.append(locality) }
            if let subLocality = placemark.subLocality { addressComponents.append(subLocality) }
            if let thoroughfare = placemark.thoroughfare { addressComponents.append(thoroughfare) }
            if let subThoroughfare = placemark.subThoroughfare { addressComponents.append(subThoroughfare) }
            
            let address = addressComponents.joined(separator: "")
            print("【定位回调】详细地址: \(address)")
            
            // 返回数据给JS
            let locationData: [String: String] = [
                "lat": "\(latitude)",
                "lng": "\(longitude)",
                "address": address
            ]
            print("【定位回调】返回完整位置数据: \(locationData)")
            self.locationResponseCallback?(["data": locationData, "status": 1, "msg": "获取位置成功"])
        }
    }
    
    // 定位失败回调
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("【定位回调】定位失败: \(error)")
        locationManager?.stopUpdatingLocation()
        
        // 使用回调返回错误
        locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位失败: \(error.localizedDescription)"])
    }
    
    // 授权状态变化回调
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("【定位回调】授权状态变化: \(status.rawValue)")
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            // 用户授权了定位权限，开始定位
            print("【定位回调】用户授权了定位权限，开始定位")
            locationManager?.startUpdatingLocation()
        case .denied, .restricted:
            // 用户拒绝了定位权限
            print("【定位回调】用户拒绝了定位权限")
            // 使用回调返回错误
            locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位权限被拒绝"])
        default:
            print("【定位回调】等待用户授权决定")
            break
        }
    }
}

// 辅助方法 (可选, 如果需要弹窗提示)
extension WebViewController {
    private func showAlert(message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
        present(alert, animated: true, completion: nil)
    }
}

// MARK: - WKScriptMessageHandler
extension WebViewController: WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        if message.name == "urlChangeObserver" {
            // 解析消息内容
            guard let body = message.body as? [String: Any],
                  let url = body["url"] as? String else {
                return
            }
            
            print("WebView URL 变化 (JS): \(url)")
            if let path = body["path"] as? String {
                print("WebView Path 变化 (JS): \(path)")
            }
            
            // 检查是否是特殊路径，需要显示导航栏
            checkAndUpdateNavBarForSpecialPaths(urlString: url)
        }
    }
}

// MARK: - Load Error Handling
extension WebViewController {
    private func handleLoadError(_ error: Error) {
        print("WebView load error: \(error.localizedDescription)")
        // 弹出错误提示
        showAlert(message: "页面加载失败，请检查网络后重试")

        // 若最初隐藏了导航栏，为避免无法返回，则强制显示
        if shouldHideNavigationBar {
            shouldHideNavigationBar = false
            updateNavigationBarVisibility()
        }

        // 更新标题
        navTitle = "加载失败"
    }
}

