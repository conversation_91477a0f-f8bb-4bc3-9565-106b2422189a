import UIKit
import HXPhotoPicker
// 如需支持Kingfisher请解开注释
// import Kingfisher

/// 图片类型枚举
enum ImageType {
    case avatar    // 头像
    case background // 背景图
}

/// 通用图片裁剪预览控制器
class ImageCropPreviewController: UIViewController {
    // MARK: - 参数
    private let image: UIImage?
    private let imageURL: URL?
    private let cropRatio: CGSize
    private let confirmButtonTitle: String
    private let descriptionText: String?
    private let imageType: ImageType // 新增：图片类型
    /// 图片和上传后 URL 的回调
    private let onImageChanged: (UIImage, String) -> Void
    
    // MARK: - UI
    private let previewImageView = UIImageView()
    private let confirmButton = UIButton(type: .system)
    private let descriptionLabel = UILabel()
    private let closeButton = UIButton(type: .custom)
    private let confirmIcon = UIImageView(image: UIImage(named: "icon_input_ImageButton"))
    
    // MARK: - 状态
    private var croppedImage: UIImage?
    
    // MARK: - 初始化
    init(image: UIImage?, imageURL: URL?, cropRatio: CGSize, confirmButtonTitle: String, descriptionText: String?, imageType: ImageType = .avatar, onImageChanged: @escaping (UIImage, String) -> Void) {
        self.image = image
        self.imageURL = imageURL
        self.cropRatio = cropRatio
        self.confirmButtonTitle = confirmButtonTitle
        self.descriptionText = descriptionText
        self.imageType = imageType
        self.onImageChanged = onImageChanged
        super.init(nibName: nil, bundle: nil)
        modalPresentationStyle = .fullScreen
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        showPreview()
    }
    
    // MARK: - UI布局
    private func setupUI() {
        view.backgroundColor = UIColor.black
        // 关闭按钮（白色X，左上角，安全区内）
        closeButton.setImage(UIImage(systemName: "xmark")?.withRenderingMode(.alwaysTemplate), for: .normal)
        closeButton.tintColor = .white
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(closeButton)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 16),
            closeButton.leftAnchor.constraint(equalTo: view.safeAreaLayoutGuide.leftAnchor, constant: 16),
            closeButton.widthAnchor.constraint(equalToConstant: 28),
            closeButton.heightAnchor.constraint(equalToConstant: 28)
        ])
        // 预览图片
        previewImageView.contentMode = .scaleAspectFit
        previewImageView.clipsToBounds = true
        previewImageView.backgroundColor = .black
        view.addSubview(previewImageView)
        previewImageView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            previewImageView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            previewImageView.widthAnchor.constraint(equalTo: view.widthAnchor),
            previewImageView.heightAnchor.constraint(equalTo: previewImageView.widthAnchor, multiplier: cropRatio.height / cropRatio.width),
            previewImageView.centerYAnchor.constraint(equalTo: view.centerYAnchor, constant: -40)
        ])
        
        // 说明文案
        if let desc = descriptionText {
            descriptionLabel.text = desc
            descriptionLabel.textColor = UIColor.white
            descriptionLabel.font = UIFont.systemFont(ofSize: 14)
            descriptionLabel.textAlignment = .center
            view.addSubview(descriptionLabel)
            descriptionLabel.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                descriptionLabel.topAnchor.constraint(equalTo: previewImageView.bottomAnchor, constant: 18),
                descriptionLabel.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 24),
                descriptionLabel.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -24)
            ])
        }
        
        // 下方按钮（左文字右图标）
        confirmButton.setTitle(confirmButtonTitle, for: .normal)
        confirmButton.setTitleColor(.white, for: .normal)
        confirmButton.titleLabel?.font = UIFont.systemFont(ofSize: 15)
        confirmButton.backgroundColor = UIColor(hex: "#3C3C3C")
        confirmButton.layer.cornerRadius = 8
        confirmButton.contentHorizontalAlignment = .left
        confirmButton.titleEdgeInsets = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 0)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        view.addSubview(confirmButton)
        confirmButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            confirmButton.leftAnchor.constraint(equalTo: view.leftAnchor, constant: 24),
            confirmButton.rightAnchor.constraint(equalTo: view.rightAnchor, constant: -24),
            confirmButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -28),
            confirmButton.heightAnchor.constraint(equalToConstant: 44)
        ])
        // 图标
        confirmIcon.contentMode = .scaleAspectFit
        view.addSubview(confirmIcon)
        confirmIcon.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            confirmIcon.centerYAnchor.constraint(equalTo: confirmButton.centerYAnchor),
            confirmIcon.rightAnchor.constraint(equalTo: confirmButton.rightAnchor, constant: -12),
            confirmIcon.widthAnchor.constraint(equalToConstant: 24),
            confirmIcon.heightAnchor.constraint(equalToConstant: 24)
        ])
    }
    
    // MARK: - 显示预览
    private func showPreview() {
        if let image = image {
            previewImageView.image = image
        } else if let url = imageURL {
            // 如需支持Kingfisher请解开注释
             previewImageView.kf.setImage(with: url)
        }
    }
    
    // MARK: - 事件
    @objc private func closeTapped() {
        dismiss(animated: true)
    }
    
    @objc private func confirmTapped() {
        // 弹出HXPhotoPicker选择器
        var config = PickerConfiguration()
        config.selectMode = .single
        config.selectOptions = .photo
        config.photoSelectionTapAction = .openEditor
        config.modalPresentationStyle = .fullScreen
        config.editor.modalPresentationStyle = .fullScreen
        config.editor.toolsView.toolOptions = [
            EditorConfiguration.ToolsView.Options(
                imageType: .local("hx_editor_photo_crop"),
                type: .cropSize
            )
        ]
        config.editor.photo.defaultSelectedToolOption = .cropSize
        config.editor.isWhetherFinishButtonDisabledInUneditedState = true
        config.editor.cropSize.aspectRatios = [
            .init(title: .custom("裁剪"), ratio: cropRatio)
        ]
        config.editor.cropSize.aspectRatio = cropRatio
        config.editor.cropSize.isFixedRatio = true
        config.editor.cropSize.defaultSeletedIndex = 0
        Task { [weak self] in
            guard let self = self else { return }
            do {
                let images: [UIImage] = try await PhotoPickerController.picker(config, fromVC: self)
                if let image = images.first, let imageData = image.jpegData(compressionQuality: 0.9) {
                    // 上传图片
                    APIManager.shared.uploadFileQNRaw(files: [imageData]) { result in
                        switch result {
                        case .success(let response):
                            print("[DEBUG] 上传成功: \(response)")
                            DispatchQueue.main.async {
                                let urlStr = response.data?.first?.data.first ?? ""
                                // 根据图片类型调用不同的处理方法
                                if self.imageType == .avatar {
                                    self.updateUserAvatar(avatarUrl: urlStr, image: image)
                                } else {
                                    // 背景图直接回调，不需要调用接口
                                    self.onImageChanged(image, urlStr)
                                    self.dismiss(animated: true)
                                }
                            }
                        case .failure(let error):
                            print("[DEBUG] 上传失败: \(error)")
                            DispatchQueue.main.async {
                                let message = self.imageType == .avatar ? "头像上传失败，请重试" : "背景图上传失败，请重试"
                                self.showToast(message)
                            }
                        }
                    }
                }
            } catch {
                print("[DEBUG] 用户取消或出错: \(error)")
            }
        }
    }
    
    private func updateUserAvatar(avatarUrl: String, image: UIImage) {
        if avatarUrl.isEmpty {
            showToast("头像上传失败，请重试")
            return
        }
        
        // 显示加载指示器
        let loadingView = UIActivityIndicatorView(style: .medium)
        loadingView.color = .white
        loadingView.startAnimating()
        view.addSubview(loadingView)
        loadingView.center = view.center
        
        // 调用头像上报接口
        APIManager.shared.setUserAvatar(AvatarUrl: avatarUrl) { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                // 移除加载指示器
                loadingView.removeFromSuperview()
                
                switch result {
                case .success:
                    print("[DEBUG] 头像上报成功")
                    // 调用回调并关闭页面
                    self.onImageChanged(image, avatarUrl)
                    self.dismiss(animated: true)
                case .failure(let error):
                    print("[DEBUG] 头像上报失败: \(error)")
                    self.showToast("头像设置失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func showToast(_ message: String) {
        let toast = UILabel()
        toast.backgroundColor = UIColor.black.withAlphaComponent(0.7)
        toast.textColor = .white
        toast.textAlignment = .center
        toast.font = UIFont.systemFont(ofSize: 14)
        toast.layer.cornerRadius = 10
        toast.clipsToBounds = true
        toast.text = message
        toast.numberOfLines = 0
        toast.alpha = 0
        
        view.addSubview(toast)
        toast.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toast.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            toast.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -100),
            toast.widthAnchor.constraint(lessThanOrEqualToConstant: 280),
            toast.heightAnchor.constraint(greaterThanOrEqualToConstant: 40)
        ])
        
        UIView.animate(withDuration: 0.3, animations: {
            toast.alpha = 1
        }) { _ in
            UIView.animate(withDuration: 0.3, delay: 2, options: [], animations: {
                toast.alpha = 0
            }) { _ in
                toast.removeFromSuperview()
            }
        }
    }
} 
